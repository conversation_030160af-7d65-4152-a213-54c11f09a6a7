import { errorHand<PERSON> } from "@/lib/api-error";
import { UserRole } from "@/lib/generated/prisma";
import { authMiddleware } from "@/lib/middleware/auth";
import { createPageView, getPageViews } from "@/services/page-view.service";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    try {
        const body = (await request.json()) as { page: string, title?: string, referrer?: string, userAgent?: string, ipAddress?: string, sessionId?: string, duration?: number };
        const result = await createPageView(body);
        return NextResponse.json(result, { status: 201 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function GET(request: NextRequest) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const page = Number(request.nextUrl.searchParams.get('page')) || 1;
        const limit = Number(request.nextUrl.searchParams.get('limit')) || 10;
        const userId = request.nextUrl.searchParams.get('userId') || undefined;
        const viewedAtFrom = request.nextUrl.searchParams.get('viewedAtFrom') ? new Date(request.nextUrl.searchParams.get('viewedAtFrom')!) : undefined;
        const viewedAtTo = request.nextUrl.searchParams.get('viewedAtTo') ? new Date(request.nextUrl.searchParams.get('viewedAtTo')!) : undefined;
        const result = await getPageViews(page, limit, userId, viewedAtFrom, viewedAtTo);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}