import { errorHand<PERSON> } from "@/lib/api-error";
import { validate } from "@/lib/middleware/validation";
import { RegisterInput, RegisterInputSchema, registerUser } from "@/services/auth.service";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    try {
    const body = (await request.json()) as RegisterInput;
    const validatedData = await validate(RegisterInputSchema, body);
    const result = await registerUser(validatedData);
    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    return errorHandler(error);
  }
}