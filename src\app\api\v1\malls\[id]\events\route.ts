import { error<PERSON><PERSON><PERSON> } from "@/lib/api-error";
import { UserRole } from "@/lib/generated/prisma";
import { EventCreateInputSchema } from "@/lib/generated/zod";
import { authMiddleware } from "@/lib/middleware/auth";
import { createEvent, getEvents } from "@/services/event.service";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const { id } = await params;
        const page = Number(request.nextUrl.searchParams.get('page')) || 1;
        const limit = Number(request.nextUrl.searchParams.get('limit')) || 10;
        const dateFrom = request.nextUrl.searchParams.get('dateFrom') ? new Date(request.nextUrl.searchParams.get('dateFrom')!) : undefined;
        const dateTo = request.nextUrl.searchParams.get('dateTo') ? new Date(request.nextUrl.searchParams.get('dateTo')!) : undefined;
        const result = await getEvents(id, page, limit, dateFrom, dateTo);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { id } = await params;
        const body = (await request.json()) as z.infer<typeof EventCreateInputSchema>;
        const result = await createEvent(body, id, decoded.userId);
        return NextResponse.json(result, { status: 201 });
    }
    catch (err) {
        return errorHandler(err);
    }
}