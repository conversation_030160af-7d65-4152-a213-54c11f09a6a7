import { Offer, OfferCreateInputSchema } from "@/lib/generated/zod";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/lib/types/api-response";
import { z } from "zod";

export const createOffer = async (
    input: z.infer<typeof OfferCreateInputSchema>,
    mallId: string,
    userId: string
): Promise<ApiResponse<Offer>> => {
    const offer = await prisma.offer.create({

        data: {
            ...input,
            mall: {
                connect: {
                    id: mallId,
                }
            },
            user: {
                connect: {
                    id: userId,
                }
            },
        },
    });

    return {
        success: true,
        data: offer,
    };
}

export const getOffer = async (offerId: string): Promise<ApiResponse<Offer | null>> => {
    const offer = await prisma.offer.findUnique({
        where: { id: offerId },
        include: {
            shopOffers: true,
        },
    });

    return {
        success: true,
        data: offer,
    };
}

export const getOffers = async (
    mallId: string,
    page: number,
    limit: number,
    shopId?: string,
    validFrom?: Date,
    validTo?: Date
): Promise<ApiResponse<Offer[]>> => {
    const skip = (page - 1) * limit;
    const take = limit;

    const offers = await prisma.offer.findMany({
        skip,
        take,
        where: {
            mallId,
            ...(shopId && { shopOffers: { some: { shopId } } }),
            ...(validFrom && { validFrom: { gte: validFrom } }),
            ...(validTo && { validTo: { lte: validTo } }),
        }
    });

    const total = await prisma.offer.count({
        where: {
            mallId,
            ...(shopId && { shopOffers: { some: { shopId } } }),
            ...(validFrom && { validFrom: { gte: validFrom } }),
            ...(validTo && { validTo: { lte: validTo } }),
        }
    });

    return {
        success: true,
        data: offers,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
}

export const updateOffer = async (
    input: Partial<z.infer<typeof OfferCreateInputSchema>>,
    offerId: string,
    userId: string
): Promise<ApiResponse<Offer>> => {
    const offer = await prisma.offer.update({
        where: { id: offerId },
        data: {
            ...input,
            user: {
                connect: {
                    id: userId,
                }
            },
        },
    });

    return {
        success: true,
        data: offer,
    };
}

export const deleteOffer = async (offerId: string): Promise<ApiResponse<{ message: string }>> => {
    await prisma.offer.delete({
        where: { id: offerId },
    });

    return {
        success: true,
        data: { message: "Offer deleted" },
    };
}