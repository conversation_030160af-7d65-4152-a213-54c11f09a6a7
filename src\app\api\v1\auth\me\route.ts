import { error<PERSON><PERSON><PERSON> } from "@/lib/api-error";
import { User } from "@/lib/generated/prisma";
import { authMiddleware } from "@/lib/middleware/auth";
import { validate } from "@/lib/middleware/validation";
import { ApiResponse } from "@/lib/types/api-response";
import { setAccessToken } from "@/services/cookie.service";
import { updateProfile, UpdateProfileInputSchema } from "@/services/user.service";
import { NextRequest, NextResponse } from "next/server";

// Get authenticated user profile
export async function GET(request: NextRequest) {
    try {
        const decoded = await authMiddleware(request);
        return NextResponse.json({ success: true, data: decoded } as ApiResponse<User>, { status: 200 });
    }
    catch (error) {
        return errorHandler(error);
    }
}

// Update authenticated user profile
export async function PUT(request: NextRequest) {
    try {
        const decoded = await authMiddleware(request);
        const body = (await request.json());
        const validatedData = await validate(UpdateProfileInputSchema, body);
        const result = await updateProfile(validatedData, decoded.userId);
        const { accessToken } = result.data as { accessToken: string };
        await setAccessToken(accessToken);
        return NextResponse.json(result, { status: 200 });
    }
    catch (error) {
        return errorHandler(error);
    }
}