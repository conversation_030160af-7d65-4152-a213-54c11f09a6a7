import { errorH<PERSON><PERSON> } from "@/lib/api-error";
import { UserRole } from "@/lib/generated/prisma";
import { OfferCreateInputSchema } from "@/lib/generated/zod";
import { authMiddleware } from "@/lib/middleware/auth";
import { deleteOffer, getOffer, updateOffer } from "@/services/offer.service";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string, offerId: string }> }) {
    try {
        const { offerId } = await params;
        const result = await getOffer(offerId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string, offerId: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { offerId } = await params;
        const body = (await request.json()) as Partial<z.infer<typeof OfferCreateInputSchema>>;
        const result = await updateOffer(body, offerId, decoded.userId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string, offerId: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { offerId } = await params;
        const result = await deleteOffer(offerId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}
