import { Mall, MallHour } from "@/lib/generated/prisma";
import { MallCreateInputSchema } from "@/lib/generated/zod";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/lib/types/api-response";
import { z } from "zod";

export const getMalls = async (page: number, limit: number, city?: string, search?: string, isActive?: boolean): Promise<ApiResponse<Mall[]>> => {
    const skip = (page - 1) * limit;
    const take = limit;

    const malls = await prisma.mall.findMany({
        skip,
        take,
        where: {
            name: {
                contains: search,
            },
            slug: {
                contains: search,
            },
            ...(city && { city }),
            ...(isActive != undefined && { isActive }),
        }
    });

    const total = await prisma.mall.count({
        where: {
            name: {
                contains: search,
            },
            slug: {
                contains: search,
            },
            ...(city && { city }),
            ...(isActive != undefined && { isActive }),
        }
    });

    return {
        success: true,
        data: malls,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
}

export const getMall = async (mallId: string): Promise<ApiResponse<Mall | null>> => {
    const mall = await prisma.mall.findUnique({
        where: { id: mallId },
        include: {
            user: {
                select: {
                    id: true,
                    name: true,
                    email: true,
                }
            },
            floors: true,
            events: true,
            offers: true,
        },
    });

    return {
        success: true,
        data: mall,
    };
}

export const createMall = async (
    input: z.infer<typeof MallCreateInputSchema>,
    userId: string
): Promise<ApiResponse<Mall>> => {
    const mall = await prisma.mall.create({
        data: {
            ...input,
            user: {
                connect: {
                    id: userId
                }
            }
        },
    });

    return {
        success: true,
        data: mall,
    };
}

export const updateMall = async (
    input: Partial<z.infer<typeof MallCreateInputSchema>>,
    mallId: string
): Promise<ApiResponse<Mall>> => {
    const mall = await prisma.mall.update({
        where: { id: mallId },
        data: input,
    });

    return {
        success: true,
        data: mall,
    };
}

export const deleteMall = async (mallId: string): Promise<ApiResponse<{ message: string }>> => {
    await prisma.mall.delete({
        where: { id: mallId },
    });

    return {
        success: true,
        data: { message: "Mall deleted" },
    };
}

export const getMallHours = async (mallId: string): Promise<ApiResponse<MallHour[]>> => {
    const hours = await prisma.mallHour.findMany({
        where: { mallId },
    });

    return {
        success: true,
        data: hours,
    };
}

export const createMallHour = async (
    input: { day: number, open: string, close: string, isClosed: boolean },
    mallId: string
): Promise<ApiResponse<MallHour>> => {
    const hour = await prisma.mallHour.create({
        data: {
            ...input,
            mall: {
                connect: {
                    id: mallId,
                }
            },
        },
    });

    return {
        success: true,
        data: hour,
    };
}

export const updateMallHour = async (
    input: Partial<{ day: number, open: string, close: string, isClosed: boolean }>,
    hourId: string
): Promise<ApiResponse<MallHour>> => {
    const hour = await prisma.mallHour.update({
        where: { id: hourId },
        data: input,
    });

    return {
        success: true,
        data: hour,
    };
}

export const deleteMallHour = async (hourId: string): Promise<ApiResponse<{ message: string }>> => {
    await prisma.mallHour.delete({
        where: { id: hourId },
    });

    return {
        success: true,
        data: { message: "Mall hour deleted" },
    };
}

