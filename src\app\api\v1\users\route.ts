import { error<PERSON><PERSON><PERSON> } from "@/lib/api-error";
import { UserRole } from "@/lib/generated/prisma";
import { authMiddleware } from "@/lib/middleware/auth";
// import { authMiddleware } from "@/lib/middleware/auth";
import { getUsers } from "@/services/user.service";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
    try {
        const decoded = await authMiddleware(request)
        if (decoded.role != UserRole.admin) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const page = Number(request.nextUrl.searchParams.get('page')) || 1;
        const limit = Number(request.nextUrl.searchParams.get('limit')) || 10;
        const role = request.nextUrl.searchParams.get('role') as UserRole | undefined;
        const isActive = request.nextUrl.searchParams.get('isActive') === 'true';
        const search = request.nextUrl.searchParams.get('search') || undefined;
        const result = await getUsers(
            page,
            limit,
            role,
            isActive,
            search,
        );
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}