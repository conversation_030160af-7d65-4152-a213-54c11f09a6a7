import type { <PERSON>ada<PERSON> } from "next";
import { Montserrat } from "next/font/google";
import "./globals.css";
// import { ThemeProvider } from "@/components/theme-provider";

const montserrat = Montserrat({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-montserrat",
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${montserrat.variable} antialiased`}
      >
        {/* <ThemeProvider attribute="class" defaultTheme="light" enableSystem > */}
          {children}
        {/* </ThemeProvider> */}
      </body>
    </html>
  );
}
