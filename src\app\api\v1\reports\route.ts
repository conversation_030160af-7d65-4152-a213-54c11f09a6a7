import { errorH<PERSON><PERSON> } from "@/lib/api-error";
import { UserRole } from "@/lib/generated/prisma";
import { ReportType } from "@/lib/generated/prisma";
import { authMiddleware } from "@/lib/middleware/auth";
import { createReport, getReports } from "@/services/report.service";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const page = Number(request.nextUrl.searchParams.get('page')) || 1;
        const limit = Number(request.nextUrl.searchParams.get('limit')) || 10;
        const type = request.nextUrl.searchParams.get('type') as ReportType | undefined;
        const dateFrom = request.nextUrl.searchParams.get('dateFrom') ? new Date(request.nextUrl.searchParams.get('dateFrom')!) : undefined;
        const dateTo = request.nextUrl.searchParams.get('dateTo') ? new Date(request.nextUrl.searchParams.get('dateTo')!) : undefined;
        const result = await getReports(page, limit, type, dateFrom, dateTo);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function POST(request: NextRequest) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const body = (await request.json()) as { title: string, description?: string, type: ReportType, data: never, filters?: never, dateFrom: Date, dateTo: Date };
        const result = await createReport(body, decoded.userId);
        return NextResponse.json(result, { status: 201 });
    }
    catch (err) {
        return errorHandler(err);
    }
}