 import { errorH<PERSON><PERSON> } from "@/lib/api-error";
import { UserRole } from "@/lib/generated/prisma";
import { authMiddleware } from "@/lib/middleware/auth";
import { createShopHour, getShopHours } from "@/services/shop.service";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest, { params }: { params: Promise<{ shopId: string }> }) {
    try {
        const { shopId } = await params;
        const result = await getShopHours(shopId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ shopId: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { shopId } = await params;
        const body = (await request.json()) as { day: number, open: string, close: string, isClosed: boolean };
        const result = await createShopHour(body, shopId);
        return NextResponse.json(result, { status: 201 });
    }
    catch (err) {
        return errorHandler(err);
    }
}