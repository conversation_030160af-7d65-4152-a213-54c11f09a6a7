
 import { error<PERSON><PERSON><PERSON> } from "@/lib/api-error";
import { validate } from "@/lib/middleware/validation";
import { LoginInput, LoginInputSchema, loginUser } from "@/services/auth.service";
import { setAccessToken } from "@/services/cookie.service";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    try {
        const body = (await request.json()) as LoginInput;
        const validatedData = await validate(LoginInputSchema, body);
        const result = await loginUser(validatedData);
        const { accessToken } = result.data as { accessToken: string };
        await setAccessToken(accessToken);
        return NextResponse.json(result, { status: 200 });
    } catch (error) {
        return errorHandler(error);
    }
}