 import { PageView } from "@/lib/generated/prisma";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/lib/types/api-response";

export const createPageView = async (
    input: { page: string, title?: string, referrer?: string, userAgent?: string, ipAddress?: string, sessionId?: string, duration?: number },
    userId?: string
): Promise<ApiResponse<{ message: string }>> => {
    await prisma.pageView.create({
        data: {
            ...input,
            userId,
        },
    });

    return {
        success: true,
        data: { message: "Page view created" },
    };
}

export const getPageViews = async (
    page: number,
    limit: number,
    userId?: string,
    viewedAtFrom?: Date,
    viewedAtTo?: Date
 ): Promise<ApiResponse<PageView[]>> => {
    const skip = (page - 1) * limit;
    const take = limit;

    const pageViews = await prisma.pageView.findMany({
        skip,
        take,
        where: {
            ...(userId && { userId }),
            ...(viewedAtFrom && { viewedAt: { gte: viewedAtFrom } }),
            ...(viewedAtTo && { viewedAt: { lte: viewedAtTo } }),
        }
    });

    const total = await prisma.pageView.count({
        where: {
            ...(userId && { userId }),
            ...(viewedAtFrom && { viewedAt: { gte: viewedAtFrom } }),
            ...(viewedAtTo && { viewedAt: { lte: viewedAtTo } }),
        }
    });

    return {
        success: true,
        data: pageViews,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
}