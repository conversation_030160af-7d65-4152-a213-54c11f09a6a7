import { ApiError } from "@/lib/api-error"
import { User, UserRole } from "@/lib/generated/prisma"
import prisma from "@/lib/prisma"
import { ApiResponse } from "@/lib/types/api-response"
import bcrypt from "bcryptjs"
import jwt, { JwtPayload } from "jsonwebtoken"
import { z } from "zod"

// Environment variables for JWT
const JWT_SECRET = process.env.JWT_SECRET || 'secret-key';
export const ACCESS_TOKEN_EXPIRY = '15m'; // Access token expires in 15 minutes
export const REFRESH_TOKEN_EXPIRY = '7d'; // Refresh token expires in 7 days

export const RegisterInputSchema = z.object({
    name: z.string().min(1),
    email: z.string().email(),
    password: z.string().min(6),
    phone: z.string().min(10).max(15).optional(),
    avatar: z.string().url().optional(),
    role: z.nativeEnum(UserRole),
})
export type RegisterInput = z.infer<typeof RegisterInputSchema>

export const LoginInputSchema = z.object({
    email: z.string().email(),
    password: z.string().min(6),
})
export type LoginInput = z.infer<typeof LoginInputSchema>



export const registerUser = async (input: RegisterInput): Promise<ApiResponse<User | null>> => {
    const { name, email, password, phone, avatar, role } = input;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
        where: {
            email
        }
    });

    if (existingUser) {
        throw new ApiError(400, "User already exists")
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const user = await prisma.user.create({
        data: {
            name,
            email,
            password: hashedPassword,
            phone,
            avatar,
            role
        }
    });

    return {
        success: true,
        data: user,
    };
}

export const loginUser = async (input: LoginInput): Promise<ApiResponse<{ user: User, accessToken: string } | null>> => {
    const { email, password } = input;

    const user = await prisma.user.findUnique({
        where: { email }
    })

    if (!user) {
        throw new ApiError(401, "User not found")
    }

    const isPasswordValid = await bcrypt.compare(password, user.password)

    if (!isPasswordValid) {
        throw new ApiError(401, "Invalid password")
    }

    const accessToken = generateAccessToken(user)

    return {
        success: true,
        data: { user, accessToken }

    }
}

// Generate access token
export const generateAccessToken = (user: User): string => {
    return jwt.sign(
        { userId: user.id, name: user.name, email: user.email, role: user.role, phone: user.phone, avatar: user.avatar } as JwtPayload,
        JWT_SECRET,
        { expiresIn: ACCESS_TOKEN_EXPIRY }
    );
}