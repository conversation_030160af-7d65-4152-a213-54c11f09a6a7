import { ShopHour } from "@/lib/generated/prisma";
import { Shop, ShopCreateInputSchema } from "@/lib/generated/zod";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/lib/types/api-response";
import { z } from "zod";

export const createShop = async (
    input: z.infer<typeof ShopCreateInputSchema>,
    mallId: string,
    userId: string
): Promise<ApiResponse<Shop>> => {
    const shop = await prisma.shop.create({
        data: {
            ...input,
            mall: {
                connect: {
                    id: mallId,
                }
            },
            user: {
                connect: {
                    id: userId,
                }
            },
        },
    });

    return {
        success: true,
        data: shop,
    };
}

export const getShops = async (
    mallId: string,
    page: number,
    limit: number,
    floorId?: string,
    category?: string,
    search?: string
): Promise<ApiResponse<Shop[]>> => {
    const skip = (page - 1) * limit;
    const take = limit;

    const shops = await prisma.shop.findMany({
        skip,
        take,
        where: {
            mallId,
            ...(floorId && { floorId }),
            ...(category && { category }),
            name: {
                contains: search,
            },
        }
    });

    const total = await prisma.shop.count({
        where: {
            mallId,
            ...(floorId && { floorId }),
            ...(category && { category }),
            name: {
                contains: search,
            },
        }
    });

    return {
        success: true,
        data: shops,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
}

export const getShop = async (shopId: string): Promise<ApiResponse<Shop | null>> => {
    const shop = await prisma.shop.findUnique({
        where: { id: shopId },
        include: {
            shopHours: true,
            shopOffers: true,
        },
    });

    return {
        success: true,
        data: shop,
    };
}

export const updateShop = async (
    input: Partial<z.infer<typeof ShopCreateInputSchema>>,
    shopId: string
): Promise<ApiResponse<Shop>> => {
    const shop = await prisma.shop.update({
        where: { id: shopId },
        data: input,
    });

    return {
        success: true,
        data: shop,
    };
}

export const deleteShop = async (shopId: string): Promise<ApiResponse<{ message: string }>> => {
    await prisma.shop.delete({
        where: { id: shopId },
    });

    return {
        success: true,
        data: { message: "Shop deleted" },
    };
}

export const getShopHours = async (shopId: string): Promise<ApiResponse<ShopHour[]>> => {
    const hours = await prisma.shopHour.findMany({
        where: { shopId },
    });

    return {
        success: true,
        data: hours,
    };
}

export const createShopHour = async (
    input: { day: number, open: string, close: string, isClosed: boolean },
    shopId: string
): Promise<ApiResponse<ShopHour>> => {
    const hour = await prisma.shopHour.create({
        data: {
            ...input,
            shop: {
                connect: {
                    id: shopId,
                }
            },
        },
    });

    return {
        success: true,
        data: hour,
    };
}

export const updateShopHour = async (
    input: Partial<{ day: number, open: string, close: string, isClosed: boolean }>,
    hourId: string
): Promise<ApiResponse<ShopHour>> => {
    const hour = await prisma.shopHour.update({
        where: { id: hourId },
        data: input,
    });

    return {
        success: true,
        data: hour,
    };
}

export const deleteShopHour = async (hourId: string): Promise<ApiResponse<{ message: string }>> => {
    await prisma.shopHour.delete({
        where: { id: hourId },
    });

    return {
        success: true,
        data: { message: "Shop hour deleted" },
    };
}